from typing import Annotated
from typing_extensions import TypedDict
from pydantic import BaseModel

from langchain.chat_models import init_chat_model
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage

from dotenv import load_dotenv

load_dotenv()

class GuardrailClassification(BaseModel):
    classification: bool

class IntentClassification(BaseModel):
    classification: str

class IntentClassificationState(TypedDict):
    messages: Annotated[list, add_messages]
    classification: str
    user_query: str
    guardrail_response: str

# this guardrail needs extensive testing this should include all 4 features we are providing

def guardrail(state: IntentClassificationState) -> IntentClassificationState: # this is not having the conversation history
    "This will act as a gaudrail while also taking the user input"

    print("=============")
    user_query = input("Enter your query: ")
    

    print("=============")
    print(user_query)

    parser = StrOutputParser()

    system_prompt_guardrail = """
        You are an expert data science expert who should classify if the current user query is related to:
            - Generative AI
            - AI
            - Data Science
            - Machine Learning, Deep Learning, NLP, CV
            - anything related to data

        IMPORTANT: You have access to the full conversation history. Consider the CONTEXT of the conversation when making your decision.

        Special rules:
        1. If the user's current message is a short response (like "yes", "no", "please", "thanks", "sure", "ok") and it's clearly a response to a previous AI question about data science topics, then classify it as "True"
        2. If the conversation is already about data science topics and the user is asking for clarification, examples, or continuation, classify as "True"
        3. If the user is asking for a summary of the conversation or previous responses, classify as "True"
        4. Only classify as "False" if the query is clearly unrelated to data science topics AND not a natural continuation of an ongoing data science conversation

        Return only one word: "True" or "False"
        """
    
    messages = state.get("messages", []) + [SystemMessage(content=system_prompt_guardrail), HumanMessage(content=user_query)]

    # Call the LLM directly
    model_guardrail = init_chat_model("gpt-4.1-mini", model_provider="openai")
    structured_model = model_guardrail.with_structured_output(GuardrailClassification)
    response = structured_model.invoke(messages)

    guardrail_response = response.classification

    print("=============")
    print(guardrail_response)
    # print(type(guardrail_response))
    
    if guardrail_response:
        print("Guardrail response is True")
        messages = state["messages"] + [
            HumanMessage(content=user_query)
        ]

        return {
            "messages": messages,
            "guardrail_response": str(guardrail_response),
            "user_query": user_query
        }
    else:
        print("Please provide a query related to Data Science, Generative AI, Machine Learning, Deep Learning, NLP, CV etc.")
        return {
            "messages": state["messages"],
            "guardrail_response": str(guardrail_response),
        }

system_prompt = """
You are an intent classifier for user queries in the domain of data science, data engineering, generative AI, and related fields.
Your task is to classify the user's query into one of the following categories:

learning_path – The user is explicitly asking for a structured, step-by-step curriculum or plan to learn the skills for a specific role. This intent should only be triggered if the user uses keywords like "learning path", "roadmap", "steps to learn", "what should I learn", "curriculum", or "how to start learning" in the context of becoming a Data Scientist, Data Engineer, Data Analyst, Prompt Engineer, or Generative AI specialist.

guidance – The user is seeking general career-oriented advice. This is the default category for career questions that are not an explicit request for a learning curriculum. It includes queries about pursuing a role (e.g., "I want to pursue prompt engineering, can you advise?"), career transitions, comparing roles, or non-technical blockers.

resources – The user is specifically and explicitly asking for learning materials like courses, tutorials, books, blogs, articles, or study platforms related to data science topics. Do not classify as resources unless resources are explicitly requested.

technical_queries – The user is asking a technical question. This includes requests for conceptual explanations, how a tool or technique works, or specific code-related issues. This category covers everything from broad conceptual questions (e.g., "I want to know more about prompt engineering," "Explain transformers") to specific implementation problems (e.g., "Why is my Python code giving an error?").

Only return the field classification as one of the above four values: learning_path, guidance, resources, or technical_queries.
Be strict and choose only one most appropriate category per query. Do not add explanations.
"""

def chatbot(state: IntentClassificationState) -> IntentClassificationState:
    "This will classify the user query into one of the four categories"

    messages = state["messages"][:-1] + [SystemMessage(content=system_prompt), HumanMessage(content=f"Here is the user query:\n{state['user_query']}")]

    model_classifier = init_chat_model("gpt-4.1-mini", model_provider="openai")
    structured_model = model_classifier.with_structured_output(IntentClassification)
    response = structured_model.invoke(messages)

    classification = response.classification
    print(f"Classification: {classification}")
    return {
        "classification": classification,
        "user_query": state["user_query"]
    }

graph_builder = StateGraph(IntentClassificationState)

graph_builder.add_node("guardrail", guardrail)
graph_builder.add_node("classifier", chatbot)

graph_builder.set_entry_point("guardrail")
graph_builder.add_edge("guardrail", "classifier")
graph_builder.add_edge("classifier", END)

graph = graph_builder.compile()

response = graph.invoke({})