from typing import Annotated
from typing_extensions import TypedDict
from pydantic import BaseModel, validator

from langchain.chat_models import init_chat_model
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import HumanMessage, SystemMessage, trim_messages
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from dotenv import load_dotenv

load_dotenv()

class GuardrailClassification(BaseModel):
    classification: bool

class MentornautState(TypedDict):
    messages: Annotated[list, add_messages]
    guardrail_response: str
    user_query: str

def guardrail(state: MentornautState) -> MentornautState: # this is not having the conversation history
    "This will act as a gaudrail while also taking the user input"

    print("=============")
    user_query = input("Enter your query: ")
    
    if user_query == "quit":
        return {
            "guardrail_response": "quit",
        }

    print("=============")
    print(user_query)

    parser = StrOutputParser()

    system_prompt_guardrail = """
        You are an expert data science expert who should classify if the current user query is related to:
            - Generative AI
            - AI
            - Data Science
            - Machine Learning, Deep Learning, NLP, CV
            - anything related to data

        IMPORTANT: You have access to the full conversation history. Consider the CONTEXT of the conversation when making your decision.

        Special rules:
        1. If the user's current message is a short response (like "yes", "no", "please", "thanks", "sure", "ok") and it's clearly a response to a previous AI question about data science topics, then classify it as "True"
        2. If the conversation is already about data science topics and the user is asking for clarification, examples, or continuation, classify as "True"
        3. If the user is asking for a summary of the conversation or previous responses, classify as "True"
        4. Only classify as "False" if the query is clearly unrelated to data science topics AND not a natural continuation of an ongoing data science conversation

        Return only one word: "True" or "False"
        """
    messages = state.get("messages", []) + [SystemMessage(content=system_prompt_guardrail), HumanMessage(content=user_query)]
    print(messages)
    messages = trim_messages(
            messages,
            max_tokens=300000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True, # Default is False
    )

    # Call the LLM directly
    model_guardrail = init_chat_model("gpt-4.1-mini", model_provider="openai")
    structured_model = model_guardrail.with_structured_output(GuardrailClassification)
    response = structured_model.invoke(messages)

    guardrail_response = response.classification

    print("=============")
    print(guardrail_response)
    # print(type(guardrail_response))
    
    if guardrail_response:
        print("Guardrail response is True")
        messages = state["messages"] + [
            HumanMessage(content=user_query)
        ]

        return {
            "guardrail_response": str(guardrail_response),
            "messages": messages, 
            "user_query": user_query
        }
    else:
        print("Please provide a query related to Data Science, Generative AI, Machine Learning, Deep Learning, NLP, CV etc.")
        return {
            "guardrail_response": str(guardrail_response),
            "messages": state["messages"]
        }

embeddings_model_name = "BAAI/bge-base-en-v1.5"
embeddings_model = HuggingFaceEmbeddings(model_name=embeddings_model_name)

vector_db = Chroma(
    persist_directory="/home/<USER>/av_projects/mentornaut/src/chroma_db_course_articles_clean_chunk", 
    collection_name="courses_articles_chunk_collection",
    embedding_function=embeddings_model
)

retriever = vector_db.as_retriever(search_kwargs={'k': 5}) # "score_threshold": 0.3 # Use K to be 5 to 10

def format_docs_mixed(docs):
    formatted_docs = []
    for doc in docs:
        metadata = doc.metadata
        content = doc.page_content
        # Check for course
        if "course_link" in metadata:
            formatted_doc = (
                f"Course Name: {metadata.get('course_name', 'N/A')}\n"
                f"Lesson: {metadata.get('lesson_name', 'N/A')}\n"
                f"Course Link: {metadata.get('course_link', 'N/A')}\n"
                f"Content:\n{content}"
            )
        # Check for article
        elif "url" in metadata:
            formatted_doc = (
                f"Article Title: {metadata.get('title', 'N/A')}\n"
                f"URL: {metadata.get('url', 'N/A')}\n"
                f"Description: {metadata.get('meta_description', 'N/A')}\n"
                f"Content:\n{content}"
            )
        else:
            formatted_doc = f"Unknown document type.\nContent:\n{content}"
        formatted_docs.append(formatted_doc)
    if not formatted_docs:
        return "No documents found."
    return "\n\n---\n\n".join(formatted_docs)

# llm = init_chat_model("anthropic:claude-3-5-haiku-latest")
llm = init_chat_model("openai:gpt-4.1-mini")

system_prompt_chatbot = """
You are a friendly assistant who will help the user with their data science and generative AI queries.

CORE PRINCIPLE: Always prioritize and directly address the user's specific query. The retrieved content below is supplementary - use it only if it directly relates to and enhances your answer to the user's question. If the retrieved content is not relevant or sufficient, ignore it completely and answer based on your knowledge.

IMPORTANT: The user works in healthcare insurance domain. When explaining concepts, use healthcare insurance examples, analogies, and use cases to make the concepts relatable and practical.

DOMAIN-SPECIFIC GUIDELINES:
- Use healthcare insurance examples (claims processing, risk assessment, patient data, medical coding, fraud detection, etc.)
- Create analogies from healthcare insurance operations (premium calculations, policy underwriting, claims validation, etc.)
- Reference healthcare insurance datasets and scenarios (patient demographics, medical histories, treatment costs, insurance coverage, etc.)
- Mention relevant healthcare insurance applications of the concepts being discussed

RESPONSE STRATEGY:
1. First, understand what the user is specifically asking
2. Answer their question directly using your knowledge
3. Only incorporate retrieved content if it adds meaningful value to your answer
4. If retrieved content is irrelevant or insufficient, proceed without it

RESPONSE STYLE:
- Keep responses concise and crisp (within 200 words)
- Use bullet points when necessary to increase readability
- Maintain a conversational tone
- Focus entirely on addressing the user's query
- Ask follow-up questions about the data science/AI concepts (NOT about healthcare insurance specifics)

Always end with an engaging question about the technical concepts to continue the learning conversation.
"""

def chatbot(state: MentornautState) -> MentornautState:
    """This function will take user query, retrieve relevant contents and return a response"""

    # rephrase the user query using llm
    system_prompt_rephrase = """
        You are an expert in retrieving information from a vector database containing prompt engineering, generative AI, and large language model content.

        You will be given a user query along with conversation context. Your task is to rephrase the query to optimize vector database retrieval while preserving all original information.

        CONTEXT HANDLING:
        - If the query is a short response (like "yes", "no", "please", "sure", "more examples") that refers to previous conversation, incorporate the relevant context into your rephrased query
        - If the query contains pronouns or references ("this", "that", "it", "the above method"), replace them with specific terms from the conversation context
        - If the query is a follow-up question, make it standalone by including necessary context

        REPHRASING GUIDELINES:
        1. Expand abbreviations and acronyms (LLM → Large Language Model, RAG → Retrieval Augmented Generation, CoT → Chain of Thought)
        2. Add relevant synonyms and alternative terms that might appear in technical documentation
        3. Include both technical and layman terms when applicable
        4. Make implicit concepts explicit (e.g., "How to improve responses?" → "How to improve large language model response quality prompt engineering techniques")
        5. Convert questions into declarative keyword-rich statements when beneficial for retrieval
        6. Preserve all technical specifics, numbers, and constraints from the original query

        EXAMPLES:
        - "Yes, show me examples" (after discussing few-shot prompting) → "few-shot prompting examples techniques demonstrations use cases"
        - "How does this work?" (referring to chain-of-thought) → "chain-of-thought prompting working mechanism step-by-step reasoning large language models"
        - "Can you give me more details?" (after discussing system prompts) → "system prompts detailed explanation instruction design role definition generative AI"

        Return ONLY the rephrased query optimized for vector database retrieval.
        """
    
    # Start with the system prompt for rephrasing
    messages = state.get("messages", []) + [SystemMessage(content=system_prompt_rephrase), HumanMessage(content=f"Here is the user query:\n{state['user_query']}")]

    messages = trim_messages(
            messages,
            max_tokens=300000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True,
    )

    model_rephrase = init_chat_model("openai:gpt-4.1-mini")
    response = model_rephrase.invoke(messages)
    rephrased_query = response.content 

    print("==============================================")
    print("REPHRASED QUERY")
    print(rephrased_query)

    response_retriever = retriever.invoke(rephrased_query)

    response_formatted = format_docs_mixed(response_retriever)

    system_prompt_with_context = f"{system_prompt_chatbot} Relevant context retrieved from the database:\n\n {response_formatted}"
    context_message = SystemMessage(content=system_prompt_with_context)

    messages_for_llm = state["messages"][:-1] + [context_message, state["messages"][-1]]

    messages_for_llm = trim_messages(
            messages_for_llm,
            max_tokens=180000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True,
    )

    # Get response from LLM with tools
    response = llm.invoke(messages_for_llm)
    print("\n")
    print("==============================================")
    print("LLM RESPONSE")
    print(response.content)
    # Return updated state with new message appended to existing messages
    return {
        "messages": state["messages"] + [response]
    }

# graph_builder = StateGraph(MentornautState)

# graph_builder.add_node("guardrail", guardrail)
# graph_builder.add_node("chatbot", chatbot)

# def guardrail_condition(state: MentornautState) -> str:
#     return state["guardrail_response"]

# graph_builder.set_entry_point("guardrail")
# graph_builder.add_conditional_edges(
#     "guardrail",
#     guardrail_condition,
#     {
#         "True" : "chatbot",
#         "False" : "guardrail", 
#         "quit": END
#     }
# )
# graph_builder.add_edge("chatbot", "guardrail")

# graph = graph_builder.compile()

graph_builder = StateGraph(MentornautState) # this graph is used in chatbot with frontend

graph_builder.add_node("guardrail", guardrail)
graph_builder.add_node("chatbot", chatbot)

def guardrail_condition(state: MentornautState) -> str:
    return state["guardrail_response"]

graph_builder.set_entry_point("guardrail")
graph_builder.add_conditional_edges(
    "guardrail",
    guardrail_condition,
    {
        "True" : "chatbot",
        "False" : END, 
        "quit": END
    }
)
# graph_builder.add_edge("chatbot", "guardrail")
graph_builder.add_edge("chatbot", END)

graph = graph_builder.compile()

graph.invoke({})