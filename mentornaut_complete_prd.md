# Project Requirements Document (PRD)
# Mentornaut: AI Career Guidance Chatbot for Data Science and AI

## 1. <PERSON><PERSON><PERSON>
<PERSON><PERSON> an intelligent conversational assistant specialized in data science and AI career guidance. The chatbot will deliver personalized learning roadmaps, curated resource recommendations, and expert advice. It supports both structured user profiling (MCQs + textbox) and unstructured text input, with dynamic conversation flows managed via a LangGraph-style state machine. It includes safeguards with guardrail classification, manages conversation context efficiently through summarization, and logs user intents for analytics.

## 2. Background & Context
Aspiring data scientists and AI professionals face an overwhelming amount of information and lack personalized guidance tailored to their career goals. Mentornaut addresses this by combining advanced large language models (LLMs), semantic search over curated educational content, and a modular, extensible conversational flow. It minimizes noise, keeps the user engaged with relevant advice, and ensures responses stay concise, encouraging, and actionable.

## 3. Objectives & Goals
- Build a chatbot specialized in data science and AI career guidance
- Support initial user profiling via MCQs and open text to personalize guidance
- Implement intent classification for both profile and free-text queries
- Use LangGraph state machine for modular and scalable conversation flow
- Incorporate guardrail classifier to reject irrelevant queries politely
- Deliver personalized learning roadmaps, career advice, and curated resource recommendations from Analytics Vidhya content
- Provide multi-step dialogues with smooth state transitions
- Maintain a conversational, concise, and encouraging tone
- Enable chat history summarization to manage token limits and session length
- Log user intents and conversation metadata for analytics and improvement
- Use Redis-backed session store for scalability and state persistence
- Support multiple conversation threads per user with history persistence

## 4. Features

### 4.1 Initial User Profiling
- Collect fixed profile info via MCQs (single and multi-choice) and an optional text box
- Profile stored in session and used to derive initial intent
- Progressive profiling during conversation to enhance personalization

### 4.2 Guardrail Query Classification
- LLM-based classifier filters user queries for relevance to data science/AI career topics
- Off-topic queries receive polite rejection or clarification prompts
- Context-aware classification considering conversation history

### 4.3 Intent Classification & Routing
- Derive intent from either user profile or free-text query using LLM classifiers
- Supported intents include: get_career_path, learn_skill, find_resources, interview_prep, etc.
- LangGraph state machine routes conversation based on detected intent

### 4.4 Conversational Guidance
- Generate concise, supportive, stepwise career advice
- Adapt responses based on user profile and conversation history
- Maintain consistent persona with encouraging but realistic tone

### 4.5 Resource Retrieval
- On user request, perform semantic vector search (ChromaDB) over Analytics Vidhya content
- Return top matching courses/articles with formatted names, descriptions, and clickable URLs
- Filter and rank resources based on user profile and conversation context

### 4.6 LangGraph State Machine Flow
- States include: profiling, guardrail, intent_from_profile, intent, chatbot, tools, end
- Transition logic dynamically routes between states based on profile presence, query, and intent
- Gracefully handle session termination commands like "quit"

### 4.7 Chat History Summarization
- Monitor conversation history length
- When exceeding threshold (e.g., 15 messages), summarize older messages using LLM
- Store summary in session to maintain context within token limits

### 4.8 Intent Logging & Analytics
- Log each turn's intent, query, timestamp, session ID, and anonymized profile info to a database
- Use logs for insights on popular topics, intent distribution, and user behavior
- Track resource recommendation effectiveness

### 4.9 Multi-Threading Support
- Users can maintain multiple distinct conversation threads
- Each thread has a unique identifier and optional user-defined name
- Threads can focus on different career goals or learning paths
- Easy switching between active threads via dropdown or command
- Complete conversation history stored per thread

### 4.10 Thread Management
- Create new thread: "/new [optional name]"
- Switch threads: "/switch [thread_id or name]"
- List active threads: "/threads"
- Archive thread: "/archive [thread_id]"
- Thread context displayed (e.g., "Current thread: Data Science Roadmap")

## 5. Functional Requirements

| ID | Requirement | Priority | Notes |
|----|-------------|----------|-------|
| FR1 | Classify query relevance using LLM guardrail | High | Reject off-topic inputs |
| FR2 | Collect and store fixed user profile | High | MCQs + text input |
| FR3 | Derive intent from profile or free-text input | High | LLM classification with fallback rules |
| FR4 | Route conversation with LangGraph state machine | High | Dynamic flow management |
| FR5 | Generate concise career guidance | High | Support multiple topics |
| FR6 | Retrieve and display resources on demand | High | Vector search with formatted results |
| FR7 | Summarize chat history beyond threshold | High | Use LLM to generate summaries |
| FR8 | Log intent and metadata per interaction | Medium | For analytics and improvements |
| FR9 | Support graceful session termination | Medium | Commands like "quit" |
| FR10 | Use Redis for session store | Medium | Scalability and persistence |
| FR11 | Support multiple conversation threads per user | High | With unique identifiers and names |
| FR12 | Maintain separate history per thread | High | With cross-thread profile sharing |
| FR13 | Provide thread management commands | Medium | Create, switch, list, archive |
| FR14 | Enable history export and deletion | Medium | Privacy controls |
| FR15 | Support cross-thread references | Low | Reference information across threads |

## 6. Non-Functional Requirements

| Category | Requirement | Notes |
|----------|-------------|-------|
| Performance | Response latency under 3 seconds | Optimize LLM calls and DB |
| Scalability | Modular LangGraph design for easy extension | Redis sessions, microservices |
| Reliability | Handle unexpected inputs gracefully | Guardrails, fallbacks |
| Usability | Conversational, clear, encouraging tone | Supportive UX |
| Security | No sensitive data stored; comply with privacy standards | Anonymize logs |

## 7. User Experience Flow

### 7.1 Welcome & Introduction
- Friendly greeting explaining Mentornaut's purpose
- Brief overview of capabilities (roadmaps, resources, advice)
- Privacy statement (data usage, anonymization)

### 7.2 Profile Collection
- Optional but recommended for personalization
- Progressive disclosure (start with 2-3 key questions)
- Clear "skip profiling" option

### 7.3 Conversation Modes
- Guided mode: Structured pathways based on common career questions
- Free exploration: Open-ended questions on any data science/AI topic
- Resource finder: Direct access to curated learning materials

### 7.4 Session Continuity
- Welcome back messaging for returning users
- "Pick up where we left off" option
- Profile update prompts after significant time gaps

## 8. Mentornaut Persona Guidelines

### 8.1 Core Personality Traits
- Supportive but not overly enthusiastic
- Knowledgeable without being condescending
- Practical and solution-oriented
- Empathetic to career transition challenges

### 8.2 Voice & Tone
- Professional yet conversational
- Concise responses (3-5 sentences ideal)
- Uses industry terminology appropriately
- Balances encouragement with realistic expectations
- Acknowledges uncertainty when appropriate

### 8.3 Response Structure
- Lead with direct answers to questions
- Follow with brief supporting context/rationale
- End with actionable next steps or follow-up questions
- Use bullet points for multi-part information

## 9. Enhanced User Profiling

### 9.1 Core Profile Questions
1. **Career Stage**: Student, Early Career, Mid-Career, Career Transition
2. **Technical Background**: None, Basic, Intermediate, Advanced
3. **Primary Goal**: Learn Fundamentals, Specialize in Area, Prepare for Job, Advance Career
4. **Time Commitment**: <5 hrs/week, 5-10 hrs/week, 10-20 hrs/week, 20+ hrs/week

### 9.2 Optional Depth Questions
- Current role/industry
- Specific skills already acquired
- Target role or position
- Learning style preference
- Resource format preference (video, text, interactive)

### 9.3 Progressive Profiling
- Collect additional information naturally during conversations
- Update profile based on questions asked and resources requested
- Periodically confirm profile accuracy with user

## 10. Architecture Overview

### 10.1 Components
- Frontend: Web or CLI chat interface supporting MCQ forms and free-text input
- Backend:
  - Guardrail module (LLM classifier)
  - Intent classification module
  - LangGraph orchestrator for conversation flow
  - Chatbot response generator (GPT-4.1-mini)
  - Vector search tool (ChromaDB + BAAI embeddings) for resource retrieval
  - History summarization module integrated before reply generation
  - Intent logging service writing to PostgreSQL or Redis
  - Thread manager for multi-threading support
- Session Store: Redis for persistence and scalability

### 10.2 Redis Schema for Threading
```
user:{user_id}:profile -> Hash of profile attributes
user:{user_id}:threads -> Set of thread_ids
user:{user_id}:active_thread -> Current active thread_id
thread:{thread_id}:metadata -> Hash (name, created_at, last_accessed)
thread:{thread_id}:messages -> List of message objects
thread:{thread_id}:summary -> Current thread summary
thread:{thread_id}:state -> Current LangGraph state
```

## 11. Error Handling & Edge Cases

### 11.1 Technical Failures
- Graceful degradation when vector search unavailable
- Fallback responses when LLM service disrupted
- Session recovery mechanisms for connection drops

### 11.2 Conversation Recovery
- Detection of misunderstandings with repair strategies
- "Let me try again" option after unsuccessful responses
- Ability to explicitly reset conversation thread

### 11.3 User Behavior Handling
- Strategies for repetitive questions
- Handling of vague or overly broad queries
- Response to potential misuse (e.g., homework solving)
- Handling emotional or crisis-related content

## 12. Implementation Priority Matrix

### 12.1 Must Have (P0)
- Basic user profiling (4 core questions)
- Intent classification for career guidance
- LangGraph conversation flow
- Guardrail for topic relevance
- Resource recommendation from curated list
- Multi-threading support for conversations

### 12.2 Should Have (P1)
- Chat history summarization
- Intent logging for analytics
- Redis session store
- Progressive profiling
- Feedback collection mechanism
- Thread management commands

### 12.3 Could Have (P2)
- Community integration features
- Progress tracking
- Advanced personalization
- Multi-modal content support
- A/B testing framework
- Cross-thread intelligence

### 12.4 Won't Have (Initial Release)
- User accounts/authentication
- Integration with job boards
- Peer connection features
- Custom resource uploading
- Video/audio interaction

## 13. Milestones & Timeline

| Milestone | Target Date | Description |
|-----------|-------------|-------------|
| PRD & Design | Completed | Defined scope and architecture |
| Guardrail Integration | Week 1 | LLM input filtering |
| User Profiling & Intent | Week 2 | Profile collection & classification |
| Career Guidance Module | Week 3 | Response generation logic |
| Retriever Tool Setup | Week 4 | ChromaDB integration |
| LangGraph Flow | Week 5 | Full conversation orchestration |
| Multi-Threading Support | Week 6 | Thread management implementation |
| History Summarization | Week 7 | Chat history management |
| Intent Logging & Analytics | Week 8 | Logging, DB schema, and analysis |
| Testing & Refinements | Week 9 | Bug fixing and user feedback |
| Deployment | Week 10 | Production release |

## 14. Risks & Mitigations

| Risk | Mitigation |
|------|------------|
| LLM misclassifies relevant queries | Prompt tuning, fallback rules, manual overrides |
| Outdated or irrelevant resource recommendations | Regularly update content and embeddings |
| Excessive response latency | Cache embeddings, batch LLM calls |
| Profile form dropout | UX improvements, allow text override |
| Session data loss | Redis persistence, backup strategies |
| Thread confusion | Clear thread labeling, context reminders |

## 15. Success Metrics
- 90%+ queries correctly classified for relevance and intent
- Average user satisfaction rating > 4/5
- 70%+ resource requests yield clicked and useful links
- Average session length indicating meaningful engagement
- Regular usage analytics show trending career topics
- Multiple threads per user with distinct purposes

## 16. Appendix: Example Prompts

### 16.1 Guardrail Classifier Prompt
```
You are a career guidance assistant specialized in data science and AI. Classify the following user query as RELEVANT or IRRELEVANT to data science or AI career guidance.

Query: "{user_query}"

Respond only with "RELEVANT" or "IRRELEVANT".
```

### 16.2 Intent Classification Prompt
```
Classify the intent of this user query into one of these categories:
- get_career_path
- learn_skill
- find_resources
- interview_prep
- other

User query: "{user_query}"

Respond with the single intent label.
```

### 16.3 Chat History Summarization Prompt
```
Summarize the following conversation between user and chatbot, focusing on career goals, skills discussed, and resource requests. Keep it brief and informative.

Conversation:
{conversation_history}
```

### 16.4 Resource Retrieval Output Format
```
Name: {course/article name}
Description: {short summary}
URL: {clickable link}
```