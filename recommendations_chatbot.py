from typing import Annotated
from typing_extensions import TypedDict
from pydantic import BaseModel

from langchain.chat_models import init_chat_model
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain.tools.retriever import create_retriever_tool
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode
from langchain_openai import ChatOpenAI
from langchain_core.messages import trim_messages

from dotenv import load_dotenv

load_dotenv()

class GuardrailClassification(BaseModel):
    classification: bool

class RecommendationsState(TypedDict):
    messages: Annotated[list, add_messages]
    guardrail_response: str
    user_query: str

def guardrail(state: RecommendationsState) -> RecommendationsState: # this is not having the conversation history
    "This will act as a gaudrail while also taking the user input"

    print("=============")
    user_query = input("Enter your query: ")
    
    if user_query == "quit":
        return {
            "guardrail_response": "quit",
        }

    print("=============")
    print(user_query)

    system_prompt_guardrail = """
    You are an expert classifier who determines if the current user query should be handled by an AI recommendation chatbot specializing in data science and AI learning resources.

    CLASSIFY AS "True" if the query is related to:
    - Data Science/Data Engineering/Data Analytics learning resources
    - Machine Learning/Deep Learning/Generative AI courses and articles
    - Course recommendations for data science, ML, AI, or generative AI
    - Article recommendations for staying updated in data science fields
    - Learning materials for specific data science tools and technologies
    - Educational content for AI and machine learning concepts
    - Resource recommendations for data analysis and visualization
    - Study materials for data science certifications and programs
    - Learning pathways and roadmaps with resource recommendations
    - Books, tutorials, and guides for data science topics
    - Online platforms and websites for data science learning
    - Recommendations for staying updated with AI/ML trends
    - Resource suggestions for specific data science skills or techniques

    CLASSIFY AS "False" if the query is about:
    - General life advice or philosophical questions ("what is life")
    - Celebrity or entertainment topics ("who is Selena Gomez")
    - Political questions ("who is prime minister")
    - Non-data science technical topics ("what is Node.js", web development, mobile app development)
    - Finance, marketing, or other non-data domains
    - Personal relationship advice
    - Health and medical advice
    - Sports, weather, or general news
    - Cooking, travel, or lifestyle topics
    - Pure technical implementation without learning context
    - Any topic unrelated to data science, AI, ML, or generative AI learning

    IMPORTANT: You have access to the full conversation history. Consider the CONTEXT of the conversation when making your decision.

    Special rules:
    1. If the user's current message is a short response (like "yes", "no", "please", "thanks", "sure", "ok") and it's clearly a response to a previous AI question about data science learning resources, then classify it as "True"
    2. If the conversation is already about data science learning topics and the user is asking for clarification, examples, or continuation, classify as "True"
    3. If the user is asking for a summary of previous resource recommendations, classify as "True"
    4. If someone asks technical questions in the context of "What resources can help me learn this?", classify as "True"
    5. Only classify as "False" if the query is clearly unrelated to data science learning resources AND not a natural continuation of an ongoing recommendation conversation

    Return only one word: "True" or "False"
    """

    messages = state.get("messages", []) + [SystemMessage(content=system_prompt_guardrail), HumanMessage(content=user_query)]

    messages = trim_messages(
            messages,
            max_tokens=300000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True, # Default is False
    )

    # Call the LLM directly
    model_guardrail = init_chat_model("gpt-4.1-mini", model_provider="openai")
    structured_model = model_guardrail.with_structured_output(GuardrailClassification)
    response = structured_model.invoke(messages)

    guardrail_response = response.classification

    print("=============")
    print(guardrail_response)
    
    if guardrail_response:
        print("Guardrail response is True")
        messages = state["messages"] + [
            HumanMessage(content=user_query)
        ]

        return {
            "guardrail_response": str(guardrail_response),
            "messages": messages, 
            "user_query": user_query
        }
    else:
        print("Please provide a query related to Data Science, Generative AI, Machine Learning, Deep Learning, NLP, CV etc.")
        return {
            "guardrail_response": str(guardrail_response),
            "messages": state["messages"]
        }

embeddings_model_name = "BAAI/bge-base-en-v1.5"
embeddings_model = HuggingFaceEmbeddings(model_name=embeddings_model_name)

articles_db = Chroma(
    persist_directory="/home/<USER>/av_projects/mentornaut/src/chroma_db_articles_bge",  # Using existing ChromaDB
    collection_name="articles_collection",
    embedding_function=embeddings_model
)

free_courses_db = Chroma(
    persist_directory="/home/<USER>/av_projects/mentornaut/src/chroma_db_free_courses_bge",  # Using existing ChromaDB
    collection_name="free_courses_collection",
    embedding_function=embeddings_model
)

articles_retriever = articles_db.as_retriever(search_kwargs={"k": 7})
free_courses_retriever = free_courses_db.as_retriever(search_kwargs={"k": 7})

def format_docs_course(docs):
    formatted_docs = []
    for doc in docs:
        metadata = doc.metadata
        formatted_doc = f"Name: {metadata.get('name', 'N/A')}\nLink: {metadata.get('link', 'N/A')}\nAbout: {metadata.get('about', 'N/A')}\nType: {metadata.get('type', 'N/A')}"
        formatted_docs.append(formatted_doc)
    return "\n\n".join(formatted_docs)

def format_docs_article(docs):
    formatted_docs = []
    for doc in docs:
        metadata = doc.metadata
        formatted_doc = f"Title: {metadata.get('title', 'N/A')}\nURL: {metadata.get('url', 'N/A')}\nDescription: {metadata.get('meta_description', 'N/A')}"
        formatted_docs.append(formatted_doc)
    return "\n\n".join(formatted_docs)

system_prompt_chatbot = """
You are a friendly assistant who specializes in recommending data science and generative AI learning resources.

CORE PRINCIPLE: Always prioritize and directly address the user's specific learning needs. You will be provided with a collection of courses and articles below. Your job is to filter through these resources and recommend only the most relevant ones that directly match the user's query.

RESOURCE FILTERING STRATEGY:
1. Carefully review all provided courses and articles
2. Filter out resources that are completely irrelevant to the user's query
3. Select only the most relevant and high-quality resources
4. Organize them in the structured format below

RESPONSE FORMAT:
Present recommended resources in this exact structure:

**COURSES:**
- **Course Name:** [Course Title]
  **Link:** [Course URL]
  **About:** [Brief description of what the course covers and how it relates to the user's query]

**ARTICLES:**
- **Article Name:** [Article Title]
  **URL:** [Article Link]
  **Description:** [Brief description of the article content and its relevance to the user's query]

RESPONSE STYLE:
- If no relevant resources are found in the provided content, clearly state this
- Maximum 5 courses and 5 articles per response to avoid overwhelming the user
- Maintain a helpful and encouraging tone

Always end with an engaging question about what specific aspect of the topic they'd like to explore further or what type of project they're planning to work on.
"""

llm = init_chat_model("anthropic:claude-3-5-haiku-latest")

def chatbot(state: RecommendationsState) -> RecommendationsState:
    """This function will take user query, retrieve relevant contents and return a response"""

    # rephrase the user query using llm
    system_prompt_rephrase = """
        You are an expert in retrieving learning resources from a vector database containing generative AI, large language models, and AI/ML courses and articles.

        You will be given a user query along with conversation context. Your task is to rephrase the query to optimize vector database retrieval of relevant courses and articles while preserving all original learning requirements.

        CONTEXT HANDLING:
        - If the query is a short response (like "yes", "no", "please", "sure", "more resources") that refers to previous conversation, incorporate the relevant generative AI topic into your rephrased query
        - If the query contains pronouns or references ("this", "that", "it", "these models"), replace them with specific generative AI terms from the conversation context
        - If the query is a follow-up question, make it standalone by including necessary learning context

        REPHRASING GUIDELINES FOR GENERATIVE AI LEARNING RESOURCES:
        1. Expand abbreviations and acronyms (LLM → Large Language Model, GPT → Generative Pre-trained Transformer, RAG → Retrieval Augmented Generation, NLP → Natural Language Processing)
        2. Add relevant learning-focused keywords (courses, tutorials, training, certification, bootcamp, guide, documentation, examples, projects)
        3. Include both foundational and advanced terms when applicable (e.g., "transformers" + "attention mechanisms fundamentals")
        4. Make learning objectives explicit (e.g., "Learn prompt engineering" → "prompt engineering courses tutorials training generative AI large language models")
        5. Add related generative AI applications (text generation, image generation, code generation, conversational AI, content creation)
        6. Include various learning formats (online courses, articles, hands-on projects, case studies, documentation)
        7. Add skill level indicators when mentioned or implied (beginner, intermediate, advanced, fundamentals, comprehensive)

        GENERATIVE AI SPECIFIC KEYWORDS TO INCLUDE:
        - Models: GPT, BERT, T5, DALL-E, Stable Diffusion, ChatGPT, Claude, Gemini
        - Concepts: transformers, attention mechanisms, fine-tuning, prompt engineering, few-shot learning, zero-shot learning
        - Applications: text generation, chatbots, content creation, code generation, image synthesis, language translation
        - Tools: OpenAI API, Hugging Face, LangChain, vector databases, embeddings

        EXAMPLES:
        - "Yes, show me more" (after discussing prompt engineering) → "prompt engineering courses tutorials training resources large language models generative AI techniques"
        - "I want to learn this" (referring to fine-tuning) → "fine-tuning courses training tutorials large language models generative AI customization resources"
        - "Can you recommend resources for beginners?" (after discussing LLMs) → "large language models beginner courses tutorials fundamentals generative AI training resources"
        - "What about RAG?" (following generative AI discussion) → "retrieval augmented generation RAG courses tutorials training vector databases generative AI resources"
        - "More on transformers please" → "transformer architecture courses tutorials training attention mechanisms generative AI deep learning resources"

        Return ONLY the rephrased query optimized for retrieving relevant generative AI learning resources.
        """
    
    # Start with the system prompt for rephrasing
    messages = state.get("messages", []) + [SystemMessage(content=system_prompt_rephrase), HumanMessage(content=f"Here is the user query:\n{state['user_query']}")]

    messages = trim_messages(
            messages,
            max_tokens=300000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True, # Default is False
    )

    model_rephrase = init_chat_model("openai:gpt-4.1-mini")
    response = model_rephrase.invoke(messages)
    rephrased_query = response.content 

    print("==============================================")
    print("REPHRASED QUERY")
    print(rephrased_query)

    articles_retriever_response = articles_retriever.invoke(rephrased_query)
    courses_retriever_response = free_courses_retriever.invoke(rephrased_query)
    articles_retriever_response_formatted = format_docs_article(articles_retriever_response)
    courses_retriever_response_formatted = format_docs_course(courses_retriever_response)

    response_formatted = f"""
    Articles:
    {articles_retriever_response_formatted}

    Courses:
    {courses_retriever_response_formatted}
    """

    system_prompt_with_context = f"{system_prompt_chatbot} Relevant context retrieved from the database:\n\n {response_formatted}"
    context_message = SystemMessage(content=system_prompt_with_context)

    messages_for_llm = state["messages"][:-1] + [context_message, state["messages"][-1]] # should I keep the system message in the beginning only? 

    messages_for_llm = trim_messages(
            messages_for_llm,
            max_tokens=180000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True,
    )

    # Get response from LLM with tools
    response = llm.invoke(messages_for_llm)
    print("\n")
    print("==============================================")
    print("LLM RESPONSE")
    print(response.content)
    # Return updated state with new message appended to existing messages
    return {
        "messages": state["messages"] + [response]
    }

# graph_builder = StateGraph(RecommendationsState)

# graph_builder.add_node("guardrail", guardrail)
# graph_builder.add_node("chatbot", chatbot)

# def guardrail_condition(state: RecommendationsState) -> str:
#     return state["guardrail_response"]

# graph_builder.set_entry_point("guardrail")
# graph_builder.add_conditional_edges(
#     "guardrail",
#     guardrail_condition,
#     {
#         "True" : "chatbot",
#         "False" : "guardrail", 
#         "quit": END
#     }
# )
# graph_builder.add_edge("chatbot", "guardrail")

# graph = graph_builder.compile()

graph_builder = StateGraph(RecommendationsState)

graph_builder.add_node("guardrail", guardrail)
graph_builder.add_node("chatbot", chatbot)

def guardrail_condition(state: RecommendationsState) -> str:
    return state["guardrail_response"]

graph_builder.set_entry_point("guardrail")
graph_builder.add_conditional_edges(
    "guardrail",
    guardrail_condition,
    {
        "True" : "chatbot",
        "False" : END, 
        "quit": END
    }
)
graph_builder.add_edge("chatbot", END)

graph = graph_builder.compile()

response = graph.invoke({})