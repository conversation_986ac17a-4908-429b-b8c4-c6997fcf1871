from typing import Annotated
from typing_extensions import TypedDict
from pydantic import BaseModel

from langchain.chat_models import init_chat_model
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_chroma import Chroma
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain.tools.retriever import create_retriever_tool
from langchain_core.tools import tool
from langgraph.prebuilt import ToolNode
from langchain_openai import ChatOpenAI
from langchain_core.messages import trim_messages

from dotenv import load_dotenv

load_dotenv()
class GuardrailClassification(BaseModel):
    classification: bool

class GuidanceState(TypedDict):
    messages: Annotated[list, add_messages]
    guardrail_response: str
    user_query: str

def guardrail(state: GuidanceState) -> GuidanceState: # this is not having the conversation history
    "This will act as a gaudrail while also taking the user input"

    print("=============")
    user_query = input("Enter your query: ")
    
    if user_query == "quit":
        return {
            "guardrail_response": "quit",
        }

    print("=============")
    print(user_query)

    system_prompt_guardrail = """
    You are an expert classifier who determines if the current user query should be handled by an AI career guidance chatbot specializing in data science and AI fields.

    CLASSIFY AS "True" if the query is related to:
    - Data Science/Data Engineering/Data Analytics career guidance
    - Machine Learning/Deep Learning/Generative AI career paths
    - Learning roadmaps and skill development for data careers
    - Course and program recommendations for data science fields
    - Career transitions into data science, ML, or AI roles
    - Technical skill requirements for data science positions
    - Educational pathways (bootcamps, certifications, degrees) for data careers
    - Industry guidance for data science fields
    - Job search strategies for data science roles
    - Networking in data science communities
    - Understanding different roles in data science ecosystem
    - Interview preparation and techniques
    - Academic research discussions with career context

    CLASSIFY AS "False" if the query is about:
    - Salary discussions or compensation details
    - Job market conditions and hiring trends
    - Career advancement in data/AI companies
    - Resume writing or portfolio creation advice
    - Specific company reviews or workplace culture
    - Pure technical implementation without career context
    - Non-data science career fields (finance, marketing, etc.)
    - Personal life advice unrelated to career
    - Off-topic conversations (weather, sports, etc.)

    IMPORTANT: You have access to the full conversation history. Consider the CONTEXT of the conversation when making your decision.

    Special rules:
    1. If the user's current message is a short response (like "yes", "no", "please", "thanks", "sure", "ok") and it's clearly a response to a previous AI question about data career topics, then classify it as "True"
    2. If the conversation is already about data science career topics and the user is asking for clarification, examples, or continuation, classify as "True"
    3. If the user is asking for a summary of previous career guidance, classify as "True"
    4. If someone asks technical questions in the context of "How do I learn this for my data science career?", classify as "True"
    5. Only classify as "False" if the query is clearly unrelated to data science career topics AND not a natural continuation of an ongoing career conversation

    Return only one word: "True" or "False"
    """

    messages = state.get("messages", []) + [SystemMessage(content=system_prompt_guardrail), HumanMessage(content=user_query)]

    messages = trim_messages(
            messages,
            max_tokens=300000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True, # Default is False
    )

    # Call the LLM directly
    model_guardrail = init_chat_model("gpt-4.1-mini", model_provider="openai")
    structured_model = model_guardrail.with_structured_output(GuardrailClassification)
    response = structured_model.invoke(messages)

    guardrail_response = response.classification

    print("=============")
    print(guardrail_response)
    
    if guardrail_response:
        print("Guardrail response is True")
        messages = state["messages"] + [
            HumanMessage(content=user_query)
        ]

        return {
            "guardrail_response": str(guardrail_response),
            "messages": messages, 
            "user_query": user_query
        }
    else:
        print("Please provide a query related to Data Science, Generative AI, Machine Learning, Deep Learning, NLP, CV etc.")
        return {
            "guardrail_response": str(guardrail_response),
            "messages": state["messages"]
        }

embeddings_model_name = "BAAI/bge-base-en-v1.5"
embeddings_model = HuggingFaceEmbeddings(model_name=embeddings_model_name)

articles_db = Chroma(
    persist_directory="/home/<USER>/av_projects/mentornaut/src/chroma_db_articles_bge",  # Using existing ChromaDB
    collection_name="articles_collection",
    embedding_function=embeddings_model
)

free_courses_db = Chroma(
    persist_directory="/home/<USER>/av_projects/mentornaut/src/chroma_db_free_courses_bge",  # Using existing ChromaDB
    collection_name="free_courses_collection",
    embedding_function=embeddings_model
)

articles_retriever = articles_db.as_retriever(search_kwargs={"k": 5})
free_courses_retriever = free_courses_db.as_retriever(search_kwargs={"k": 5})

def format_docs_course(docs):
    formatted_docs = []
    for doc in docs:
        metadata = doc.metadata
        formatted_doc = f"Name: {metadata.get('name', 'N/A')}\nLink: {metadata.get('link', 'N/A')}\nAbout: {metadata.get('about', 'N/A')}\nType: {metadata.get('type', 'N/A')}"
        formatted_docs.append(formatted_doc)
    return "\n\n".join(formatted_docs)

def format_docs_article(docs):
    formatted_docs = []
    for doc in docs:
        metadata = doc.metadata
        formatted_doc = f"Title: {metadata.get('title', 'N/A')}\nURL: {metadata.get('url', 'N/A')}\nDescription: {metadata.get('meta_description', 'N/A')}"
        formatted_docs.append(formatted_doc)
    return "\n\n".join(formatted_docs)

@tool
def retriever_courses_articles_tool(llm_query: str):
    """This function will take in user query and return the courses and articles"""

    courses = free_courses_retriever.invoke(llm_query)
    courses_formatted = format_docs_course(courses)
    articles = articles_retriever.invoke(llm_query)
    articles_formatted = format_docs_article(articles)
    combined_response = f"Courses: {courses_formatted}\nArticles: {articles_formatted}"
    return combined_response

tools = [retriever_courses_articles_tool]


# llm = init_chat_model("anthropic:claude-3-5-haiku-latest")
llm = init_chat_model("openai:gpt-4.1-mini")
llm_with_tools = llm.bind_tools(tools)

system_prompt_career_guidance = """
You are an AI/Data Science career expert.

EXPERTISE: Generative AI, Data Science, ML/DL, Data Engineering, Career Strategy

PROCESS:
1. Guide: Provide a realistic, step-by-step learning roadmap with achievable timelines. 
2. Keep the guidance concise and crisp and act human.
3. Suggest resourses only when user asks for it. 
4. Resource: Only use the retriever_courses_articles_tool when requested for materials.

RESOURCE GUIDELINES:
- Use only Analytics Vidhya content for courses and articles.
- You only have resources of Data Science, Generative AI, Machine Learning, Deep Learning, NLP, CV
- Ensure relevance to the user's career goals, filtering based on specific needs.
- Provide resources solely from the tool's results, no other sources.

When Asked for resources, use the 
OUTPUT FORMAT:
always provide link or url for resources.
- Courses:
    Course 1: 
        Course Name: 
        Course Link: 
        Course About: 
- Articles:
    Article 1: 
        Article Name: 
        Article URL: 
        Article Description: 

APPROACH:
- Be conversational and human, yet direct.
- Ask targeted follow-up questions to personalize guidance.
- Provide actionable next steps with a clear, achievable roadmap.

THINGS TO KEEP IN MIND:
- Keep the word count within 200 words.
- Give a explaination of the path then provide resources if the user asks for it. 
- Use the retriever_courses_articles_tool to suggest some resources for user else do not use it.
- always provie link or url for resources. 

Your tone should be encouraging and supportive, but always grounded in honesty. Make sure the guidance feels tailored, practical, and achievable within the given timelines.
"""

def chatbot(state: GuidanceState) -> GuidanceState:
    """This function will take user query, retrieve relevant contents and return a response"""

    context_message = SystemMessage(content=system_prompt_career_guidance)

    # Check if the last message is a tool message and ensure we have at least 2 messages
    if len(state["messages"]) >= 2 and hasattr(state["messages"][-2], "tool_calls") and len(state["messages"][-2].tool_calls) > 0:
        print("including tool message")
        messages_for_llm = state["messages"][:-2] + [context_message] + state["messages"][-2:]
    else:
        messages_for_llm = state["messages"][:-1] + [context_message, state["messages"][-1]]

    messages_for_llm = trim_messages(
            messages_for_llm,
            max_tokens=180000,
            strategy="last",
            token_counter=ChatOpenAI(model="gpt-4o"),
            include_system=True,
            allow_partial=True,
    )

    # Get response from LLM with tools
    response = llm_with_tools.invoke(messages_for_llm)
    print("\n")
    print("==============================================")
    print("LLM RESPONSE")
    print(response.content)
    # Return updated state with new message appended to existing messages
    return {
        "messages": state["messages"] + [response]
    }

# graph_builder = StateGraph(GuidanceState)

# graph_builder.add_node("guardrail", guardrail)
# graph_builder.add_node("chatbot", chatbot)
# graph_builder.add_node("tools", ToolNode(tools=tools))

# def guardrail_condition(state: GuidanceState) -> str:
#     return state["guardrail_response"]

# def tool_use_condition(state: GuidanceState) -> str:
#     """
#     Routes the chatbot response based on whether it requires tools or not.
#     """
#     if not state.get("messages"):
#         raise ValueError(f"No messages found in input state: {state}")

#     ai_message = state["messages"][-1]
    
#     if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0:
#         print("returning tools")
#         return "tools"
#     print("returning gaurdrail")
#     return "no_tools"

# graph_builder.set_entry_point("guardrail")
# graph_builder.add_conditional_edges(
#     "guardrail",
#     guardrail_condition,
#     {
#         "True" : "chatbot",
#         "False" : "guardrail", 
#         "quit": END
#     }
# )
# graph_builder.add_conditional_edges(
#     "chatbot",
#     tool_use_condition, 
#     {
#         "tools" : "tools",
#         "no_tools" : "guardrail"
#     }
#     )
# graph_builder.add_edge("tools", "chatbot")

# graph = graph_builder.compile()

graph_builder = StateGraph(GuidanceState)

graph_builder.add_node("guardrail", guardrail)
graph_builder.add_node("chatbot", chatbot)
graph_builder.add_node("tools", ToolNode(tools=tools))

def guardrail_condition(state: GuidanceState) -> str:
    return state["guardrail_response"]

def tool_use_condition(state: GuidanceState) -> str:
    """
    Routes the chatbot response based on whether it requires tools or not.
    """
    if not state.get("messages"):
        raise ValueError(f"No messages found in input state: {state}")

    ai_message = state["messages"][-1]
    
    if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0:
        print("returning tools")
        return "tools"
    print("returning gaurdrail")
    return "no_tools"

graph_builder.set_entry_point("guardrail")
graph_builder.add_conditional_edges(
    "guardrail",
    guardrail_condition,
    {
        "True" : "chatbot",
        "False" : END, 
        "quit": END
    }
)
graph_builder.add_conditional_edges(
    "chatbot",
    tool_use_condition, 
    {
        "tools" : "tools",
        "no_tools" : END
    }
    )
graph_builder.add_edge("tools", "chatbot")

graph = graph_builder.compile()

graph.invoke({})