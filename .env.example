# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/mentornaut

# ChromaDB Configuration
CHROMA_DB_PATH=./data/chroma_db
ARTICLES_DB_PATH=./data/chroma_db_articles
COURSES_DB_PATH=./data/chroma_db_courses

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
DEBUG=True

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/mentornaut.log

# Session Configuration
SESSION_TIMEOUT=3600
MAX_CONVERSATION_LENGTH=15
